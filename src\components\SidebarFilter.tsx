import React, { useRef, useState, useEffect } from "react";
import { X, Check } from "react-feather";
import LocationDropdown from "./LocationDropdown";
import { Input } from "./ui/input";
import { useGoogleMaps } from "@/context/GoogleMapsContext";

const Category = [
  // "My Feed",
  "Music",
  "Literature",
  "Art",
  "Theatre & Performance",
  "Film & Photography",
  "Multidisciplinary",
  "Groups",
];

const Dateofpublishing = ["Past 24 hours", "Past week", "Past month"];
const ProfileName = ["Tom Li", "Kim Li 2"];
const Postlocation = ["United Kingdom", "United States", "United States 2"];

export default function SidebarFilter({
  onOpenChange,
  onApply,
  filters = { category: [], profileNames: [], location: [], date_of_publishing: "" },
}: {
  onOpenChange: () => void;
  onApply: (filters: {
    category: string[];
    profileNames: string[];
    location: string[];
    date_of_publishing: string;
  }) => void;
  filters?: {
    category: string[];
    profileNames: string[];
    location: string[];
    date_of_publishing: string;
  };
}) {
  const [activeSheet, setActiveSheet] = useState(1);
  const showSheet = (sheetNumber: number) => setActiveSheet(sheetNumber);

  const [selectedCategories, setSelectedCategories] = useState<number[]>([]);
  const [selectedProfileNames, setSelectedProfileNames] = useState<number[]>([]);
  const [selectedPostLocations, setSelectedPostLocations] = useState<number[]>([]);
  const [selectedDates, setSelectedDates] = useState<number[]>([]);

  // Sync local state with parent filters prop
  React.useEffect(() => {
    // Helper to map filter values to indices
    const getIndices = (values: string[], options: string[]) =>
      values.map((val) => options.indexOf(val)).filter((i) => i !== -1);
    setSelectedCategories(getIndices(filters.category || [], Category));
    setSelectedProfileNames(getIndices(filters.profileNames || [], ProfileName));
    setSelectedPostLocations(getIndices(filters.location || [], Postlocation));
    setSelectedLocations(filters.location || []);
    setSelectedDates(
      getIndices(filters.date_of_publishing ? [filters.date_of_publishing] : [], Dateofpublishing)
    );
  }, [filters]);

  const toggleCategory = (index: number) => {
    setSelectedCategories((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    );
  };

  const toggleProfileName = (index: number) => {
    setSelectedProfileNames((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    );
  };

  const togglePostLocation = (index: number) => {
    setSelectedPostLocations((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    );
  };

  const toggleDate = (index: number) => {
    setSelectedDates((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    );
  };

  // for location input field
  const locationInputRef = useRef<HTMLInputElement>(null);
  const [locationInput, setLocationInput] = useState("");
  const [selectedLocations, setSelectedLocations] = useState<string[]>([]);
  const [placePredictions, setPlacePredictions] = useState<
    google.maps.places.AutocompletePrediction[]
  >([]);
  const [showPlacesDropdown, setShowPlacesDropdown] = useState(false);
  const autocompleteService = useRef<google.maps.places.AutocompleteService | null>(null);
  const placesServiceLoaded = useRef(false);

  // Use Google Maps context
  const { isLoaded, placesService } = useGoogleMaps();

  // Initialize Google Maps API
  useEffect(() => {
    // When the Google Maps API is loaded via the context, set up the autocomplete service
    if (isLoaded && placesService && !placesServiceLoaded.current) {
      placesServiceLoaded.current = true;
      autocompleteService.current = placesService;
    }
  }, [isLoaded, placesService]);

  // Handle location input change and fetch place predictions
  const handleLocationInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLocationInput(value);

    if (value.trim() === "") {
      setPlacePredictions([]);
      setShowPlacesDropdown(false);
      return;
    }

    if (autocompleteService.current && placesServiceLoaded.current) {
      autocompleteService.current.getPlacePredictions(
        {
          input: value,
          types: ["(cities)"],
        },
        (predictions, status) => {
          if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
            setPlacePredictions(predictions);
            setShowPlacesDropdown(true);
          } else {
            setPlacePredictions([]);
            setShowPlacesDropdown(false);
          }
        }
      );
    }
  };

  // Select a place from the dropdown
  const selectPlace = (place: google.maps.places.AutocompletePrediction) => {
    const locationName = place.description;

    // Check if location is already selected
    if (!selectedLocations.includes(locationName)) {
      setSelectedLocations((prev) => [...prev, locationName]);
    }

    // Clear the input and hide dropdown
    setLocationInput("");
    setShowPlacesDropdown(false);
  };

  // Remove a selected location
  const removeLocation = (locationToRemove: string) => {
    setSelectedLocations((prev) => prev.filter((loc) => loc !== locationToRemove));
  };

  return (
    <div
      className=" flex items-center justify-center "
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
      onMouseDown={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
    >
      <div
        className="bg-white w-[22rem] max-md:w-full rounded-lg  relative"
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
        onMouseDown={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
      >
        {activeSheet === 1 && (
          <>
            <div>
              <div className="flex justify-between items-center border-b px-4 py-3">
                <button className="text-gray-500" onClick={() => onOpenChange()}>
                  <X />
                </button>
                <p className="text-lg font-bold text-titleLabel">Filter Posts</p>
                <button
                  className={`text-lg font-bold ${
                    selectedCategories.length > 0 ||
                    selectedProfileNames.length > 0 ||
                    selectedPostLocations.length > 0 ||
                    selectedLocations.length > 0 ||
                    selectedDates.length > 0
                      ? "text-primary"
                      : "text-borderColor"
                  }`}
                  onClick={() => {
                    setSelectedCategories([]);
                    setSelectedProfileNames([]);
                    setSelectedPostLocations([]);
                    setSelectedDates([]);
                    setSelectedLocations([]);
                    onApply({
                      category: [],
                      profileNames: [],
                      location: [],
                      date_of_publishing: "",
                    });
                    // onOpenChange();
                  }}
                >
                  Clear
                </button>
              </div>
              <div className="p-4">
                <div
                  className="filter-list cursor-pointer flex justify-between items-center mb-4"
                  onClick={() => showSheet(2)}
                >
                  <p className="text-titleLabel">Category</p>
                  <div className="flex gap-3 items-center">
                    <p className="text-borderColor">Any</p>
                    <span>&#8250;</span>
                  </div>
                </div>
                <div
                  className="filter-list cursor-pointer flex justify-between items-center mb-4"
                  onClick={() => showSheet(3)}
                >
                  <p className="text-titleLabel">Date of publishing</p>
                  <div className="flex gap-3 items-center">
                    <p className="text-borderColor">Any</p>
                    <span>&#8250;</span>
                  </div>
                </div>
                <div
                  className="filter-list cursor-pointer flex justify-between items-center mb-4"
                  onClick={() => showSheet(4)}
                >
                  <p className="text-titleLabel">Post location</p>
                  <div className="flex gap-3 items-center">
                    <p className="text-borderColor">Any</p>
                    <span>&#8250;</span>
                  </div>
                </div>
                <div
                  className="filter-list cursor-pointer flex justify-between items-center"
                  onClick={() => showSheet(5)}
                >
                  <p className="text-titleLabel">Profile name</p>
                  <div className="flex gap-3 items-center">
                    <p className="text-borderColor">Any</p>
                    <span>&#8250;</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="absolute bottom-0 left-0 w-full p-4 bg-white border-t">
              <button
                className="w-full bg-primary text-white py-2 rounded"
                onClick={() => {
                  onApply({
                    category: selectedCategories.map((i) => Category[i]),
                    profileNames: selectedProfileNames.map((i) => ProfileName[i]),
                    location: selectedLocations,
                    date_of_publishing: selectedDates.map((i) => Dateofpublishing[i])[0],
                  });
                }}
              >
                Apply
              </button>
            </div>
          </>
        )}
        {activeSheet === 2 && (
          <div className="flex flex-col h-full">
            <div className="flex justify-between items-center border-b px-4 py-3">
              <button
                className="flex items-center gap-2 text-gray-500"
                onClick={() => showSheet(1)}
              >
                &#8249; Back
              </button>
              <p className="text-base font-bold text-titleLabel">Category</p>
            </div>
            <div className="flex-1 p-4 overflow-y-auto">
              {Category.map((item, index) => (
                <div
                  key={index}
                  className="filter-list flex justify-between items-center mb-4 cursor-pointer"
                  onClick={() => toggleCategory(index)}
                >
                  <p className="text-titleLabel">{item}</p>
                  {selectedCategories.includes(index) && (
                    <Check color="#25282B" strokeWidth="1.6px" />
                  )}
                </div>
              ))}
            </div>
            <div className="p-4 border-t">
              <button
                className="w-full bg-primary text-white py-2 rounded"
                onClick={() => showSheet(1)}
              >
                Done
              </button>
            </div>
          </div>
        )}
        {activeSheet === 3 && (
          <div className="flex flex-col h-full">
            <div className="flex justify-between items-center border-b px-4 py-3">
              <button
                className="flex items-center gap-2 text-gray-500"
                onClick={() => showSheet(1)}
              >
                &#8249; Back
              </button>
              <p className="text-base font-bold text-titleLabel">Date of publishing</p>
            </div>
            <div className="flex-1 p-4 overflow-y-auto">
              {Dateofpublishing.map((item, index) => (
                <div
                  key={index}
                  className="filter-list flex justify-between items-center mb-4 cursor-pointer"
                  onClick={() => toggleDate(index)}
                >
                  <p className="text-titleLabel">{item}</p>
                  {selectedDates.includes(index) && <Check color="#25282B" strokeWidth="1.6px" />}
                </div>
              ))}
            </div>
            <div className="p-4 border-t">
              <button
                className="w-full bg-primary text-white py-2 rounded"
                onClick={() => showSheet(1)}
              >
                Done
              </button>
            </div>
          </div>
        )}
        {activeSheet === 4 && (
          <div
            className="flex flex-col h-full"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
          >
            <div className="flex justify-between items-center border-b px-4 py-3">
              <button
                className="flex items-center gap-2 text-gray-500"
                onClick={() => showSheet(1)}
              >
                &#8249; Back
              </button>
              <p className="text-base font-bold text-titleLabel">Post location</p>
            </div>
            <div
              className="flex-1 p-4 overflow-y-auto"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              {/* Selected Locations Display */}
              {selectedLocations.length > 0 && (
                <div
                  className="mb-4"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                >
                  <p className="text-sm font-medium text-titleLabel mb-2">Selected Locations:</p>
                  <div className="flex flex-wrap gap-2">
                    {selectedLocations.map((location, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-2 bg-primary/10 text-primary px-3 py-1 rounded-full text-sm"
                      >
                        <span>{location}</span>
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            removeLocation(location);
                          }}
                          className="hover:bg-primary/20 rounded-full p-0.5 transition-colors"
                          aria-label={`Remove ${location}`}
                        >
                          <X size={14} />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Location Input */}
              <div
                className="relative"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
              >
                <Input
                  ref={locationInputRef}
                  placeholder="Search for a location..."
                  className="resize-none h-[40px] outline-none text-lg text-primary"
                  value={locationInput}
                  onChange={handleLocationInputChange}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  aria-label="location"
                />
                <div className="">
                  <LocationDropdown
                    predictions={placePredictions}
                    onSelect={selectPlace}
                    inputRef={locationInputRef}
                    isVisible={showPlacesDropdown && placePredictions.length > 0}
                  />
                </div>
              </div>
            </div>
            <div className="p-4 border-t">
              <button
                className="w-full bg-primary text-white py-2 rounded"
                onClick={() => showSheet(1)}
              >
                Done
              </button>
            </div>
          </div>
        )}
        {activeSheet === 5 && (
          <div className="flex flex-col h-full">
            <div className="flex justify-between items-center border-b px-4 py-3">
              <button
                className="flex items-center gap-2 text-gray-500"
                onClick={() => showSheet(1)}
              >
                &#8249; Back
              </button>
              <p className="text-base font-bold text-titleLabel">Profile name</p>
            </div>
            <div className="flex-1 p-4 overflow-y-auto">
              {ProfileName.map((item, index) => (
                <div
                  key={index}
                  className="filter-list flex justify-between items-center mb-4 cursor-pointer"
                  onClick={() => toggleProfileName(index)}
                >
                  <p className="text-titleLabel">{item}</p>
                  {selectedProfileNames.includes(index) && (
                    <Check color="#25282B" strokeWidth="1.6px" />
                  )}
                </div>
              ))}
            </div>
            <div className="p-4 border-t">
              <button
                className="w-full bg-primary text-white py-2 rounded"
                onClick={() => showSheet(1)}
              >
                Done
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
