import { NextRequest, NextResponse } from 'next/server';
import { Stripe } from 'stripe';
import { getEscrowTransactionByOrderId, releaseEscrowStage } from '@/services/transactionService';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// This endpoint will be called when order status changes
export async function POST(request: NextRequest) {
  try {
    const {
      orderId,
      newStatus, // The new order status
      chargeId // Required for creating transfers
    } = await request.json();

    // Validate required fields
    if (!orderId || !newStatus || !chargeId) {
      return NextResponse.json({
        error: 'Missing required fields: orderId, newStatus, chargeId'
      }, { status: 400 });
    }

    // Get escrow transaction
    const transactionResult = await getEscrowTransactionByOrderId(orderId);
    if (!transactionResult.success || !transactionResult.transaction) {
      return NextResponse.json({
        error: 'Escrow transaction not found for this order'
      }, { status: 404 });
    }

    const transaction = transactionResult.transaction;

    // Map order status to escrow stage
    let escrowStage: 'accept' | 'delivered' | 'completed' | null = null;
    
    switch (newStatus.toLowerCase()) {
      case 'accept':
      case 'accepted':
        escrowStage = 'accept';
        break;
      case 'delivered':
        escrowStage = 'delivered';
        break;
      case 'completed':
      case 'complete':
        escrowStage = 'completed';
        break;
      default:
        return NextResponse.json({
          message: `No escrow release needed for status: ${newStatus}`
        });
    }

    // Check if this stage exists and is not already released
    const stageData = transaction.escrowStages?.find(s => s.stage === escrowStage);
    if (!stageData) {
      return NextResponse.json({
        error: `Escrow stage '${escrowStage}' not found in transaction`
      }, { status: 400 });
    }

    if (stageData.status === 'released') {
      return NextResponse.json({
        message: `Escrow stage '${escrowStage}' has already been released`
      });
    }

    // Get the original charge to ensure currency consistency
    let chargeCurrency;
    try {
      const charge = await stripe.charges.retrieve(chargeId);
      chargeCurrency = charge.currency;
      console.log('Original charge currency:', chargeCurrency);
      console.log('Transaction currency:', transaction.currency);
    } catch (error) {
      console.error('Error retrieving charge:', error);
      return NextResponse.json({
        error: 'Failed to retrieve original charge information'
      }, { status: 500 });
    }

    // Ensure currency consistency
    if (chargeCurrency !== transaction.currency) {
      console.error('Currency mismatch:', { chargeCurrency, transactionCurrency: transaction.currency });
      return NextResponse.json({
        error: `Currency mismatch: charge is in ${chargeCurrency} but transaction is in ${transaction.currency}`
      }, { status: 400 });
    }

    // Create Stripe transfer to seller
    try {
      const transfer = await stripe.transfers.create({
        amount: Math.round(stageData.amount * 100), // Convert to cents
        currency: chargeCurrency, // Use the charge currency to ensure consistency
        source_transaction: chargeId,
        destination: transaction.sellerStripeAccountId!,
        metadata: {
          orderId,
          stage: escrowStage,
          transactionId: transaction.id,
          sellerId: transaction.sellerId!,
          autoRelease: 'true',
          orderStatus: newStatus
        }
      });

      // Update escrow stage status
      const releaseResult = await releaseEscrowStage(
        transaction.id,
        escrowStage,
        transfer.id
      );

      if (!releaseResult.success) {
        return NextResponse.json({
          error: 'Failed to update escrow stage status'
        }, { status: 500 });
      }

      return NextResponse.json({
        success: true,
        transferId: transfer.id,
        amount: stageData.amount,
        stage: escrowStage,
        orderId,
        orderStatus: newStatus,
        message: `Automatically released ${escrowStage} stage payment of $${stageData.amount.toFixed(2)} due to order status change to '${newStatus}'`
      });

    } catch (stripeError) {
      console.error('Stripe transfer error:', stripeError);
      return NextResponse.json({
        error: 'Failed to create Stripe transfer'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Auto escrow release error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

// GET endpoint to check what stages would be triggered by a status change
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get('orderId');
    const status = searchParams.get('status');

    if (!orderId || !status) {
      return NextResponse.json({
        error: 'orderId and status parameters are required'
      }, { status: 400 });
    }

    // Get escrow transaction
    const transactionResult = await getEscrowTransactionByOrderId(orderId);
    if (!transactionResult.success || !transactionResult.transaction) {
      return NextResponse.json({
        error: 'Escrow transaction not found for this order'
      }, { status: 404 });
    }

    const transaction = transactionResult.transaction;

    // Map order status to escrow stage
    let escrowStage: 'accept' | 'delivered' | 'completed' | null = null;
    
    switch (status.toLowerCase()) {
      case 'accept':
      case 'accepted':
        escrowStage = 'accept';
        break;
      case 'delivered':
        escrowStage = 'delivered';
        break;
      case 'completed':
      case 'complete':
        escrowStage = 'completed';
        break;
    }

    if (!escrowStage) {
      return NextResponse.json({
        willTriggerRelease: false,
        message: `Status '${status}' does not trigger any escrow release`
      });
    }

    const stageData = transaction.escrowStages?.find(s => s.stage === escrowStage);
    if (!stageData) {
      return NextResponse.json({
        willTriggerRelease: false,
        message: `Escrow stage '${escrowStage}' not found`
      });
    }

    return NextResponse.json({
      willTriggerRelease: stageData.status !== 'released',
      stage: escrowStage,
      amount: stageData.amount,
      percentage: stageData.percentage,
      currentStatus: stageData.status,
      message: stageData.status === 'released' 
        ? `Stage '${escrowStage}' has already been released`
        : `Status '${status}' will trigger release of ${stageData.percentage}% ($${stageData.amount.toFixed(2)}) to seller`
    });

  } catch (error) {
    console.error('Auto escrow preview error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
