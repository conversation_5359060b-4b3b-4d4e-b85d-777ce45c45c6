"use client";
import React, { createContext, useContext, useState, ReactNode } from "react";

export interface FilterState {
  categories?: string[];
  dateOfPublishing?: string;
  profileName?: string;
  postLocation?: string;
  locations?: string[];
  profiles?: any[];
}

// Import the enum from the service
import { PublishingDateFilter } from "@/services/filtersServices";

// Service filter interface matching the backend
export interface ServiceFilters {
  user_id?: string[];
  date_of_publishing?: PublishingDateFilter;
  location?: string[];
}

interface FilterContextType {
  filters: FilterState;
  setFilters: (filters: FilterState) => void;
  clearFilters: () => void;
  applyFilters: (newFilters: FilterState) => void;
  getServiceFilters: () => ServiceFilters;
}

const FilterContext = createContext<FilterContextType | undefined>(undefined);

export const useFilter = () => {
  const context = useContext(FilterContext);
  if (!context) {
    throw new Error("useFilter must be used within a FilterProvider");
  }
  return context;
};

interface FilterProviderProps {
  children: ReactNode;
}

export const FilterProvider: React.FC<FilterProviderProps> = ({ children }) => {
  const [filters, setFiltersState] = useState<FilterState>({});

  const setFilters = (newFilters: FilterState) => {
    setFiltersState(newFilters);
  };

  const clearFilters = () => {
    setFiltersState({});
  };

  const applyFilters = (newFilters: FilterState) => {
    console.log("FilterContext: applyFilters called with:", newFilters);
    setFiltersState((prev) => {
      const updatedFilters = {
        ...prev,
        ...newFilters,
      };
      console.log("FilterContext: Updated filters state:", updatedFilters);
      return updatedFilters;
    });
  };

  const getServiceFilters = (): ServiceFilters => {
    console.log("FilterContext: getServiceFilters called with filters:", filters);
    const serviceFilters: ServiceFilters = {};

    if (filters.dateOfPublishing) {
      // Convert string to enum
      const dateFilter = Object.values(PublishingDateFilter).find(
        (value) => value === filters.dateOfPublishing
      );
      if (dateFilter) {
        serviceFilters.date_of_publishing = dateFilter;
      }
    }

    // Handle both old postLocation and new locations array
    const locationArray: string[] = [];
    if (filters.postLocation) {
      locationArray.push(filters.postLocation);
    }
    if (filters.locations && filters.locations.length > 0) {
      locationArray.push(...filters.locations);
      console.log("FilterContext: Added locations to filter:", filters.locations);
    }
    if (locationArray.length > 0) {
      serviceFilters.location = locationArray;
    }

    // Handle both old profileName and new profiles array
    const userIdArray: string[] = [];
    if (filters.profileName) {
      userIdArray.push(filters.profileName);
    }
    if (filters.profiles && filters.profiles.length > 0) {
      console.log("FilterContext: Processing profiles:", filters.profiles);
      // Extract user IDs from profile objects
      const profileUserIds = filters.profiles
        .map((profile) => profile.id || profile.user_id)
        .filter((id) => id); // Filter out undefined/null values
      userIdArray.push(...profileUserIds);
      console.log("FilterContext: Extracted user IDs:", profileUserIds);
    }
    if (userIdArray.length > 0) {
      serviceFilters.user_id = userIdArray;
    }

    console.log("FilterContext: Final service filters:", serviceFilters);
    return serviceFilters;
  };

  return (
    <FilterContext.Provider
      value={{ filters, setFilters, clearFilters, applyFilters, getServiceFilters }}
    >
      {children}
    </FilterContext.Provider>
  );
};

export default FilterProvider;
