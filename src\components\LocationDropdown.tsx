"use client";

import React, { useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";

interface LocationDropdownProps {
  predictions: google.maps.places.AutocompletePrediction[];
  onSelect: (place: google.maps.places.AutocompletePrediction) => void;
  inputRef: React.RefObject<HTMLInputElement>;
  isVisible: boolean;
}

const LocationDropdown: React.FC<LocationDropdownProps> = ({
  predictions,
  onSelect,
  inputRef,
  isVisible,
}) => {
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState<"top" | "bottom">("bottom");
  const [isMobile, setIsMobile] = useState(false);
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);
  const initialViewportHeight = useRef<number>(0);

  // Detect mobile device on mount - force to true for now to ensure dropdown appears above
  useEffect(() => {
    // Force mobile mode to true to ensure dropdown always appears above the input
    // This is a temporary fix to ensure the dropdown is always visible
    setIsMobile(true);

    // Original mobile detection logic (commented out for now)
    /*
    const checkIfMobile = () => {
      const userAgent = navigator.userAgent.toLowerCase();
      const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(
        userAgent
      );
      const isSmallScreen = window.innerWidth <= 768;
      setIsMobile(isMobileDevice || isSmallScreen);
    };

    checkIfMobile();
    window.addEventListener("resize", checkIfMobile);

    return () => {
      window.removeEventListener("resize", checkIfMobile);
    };
    */
  }, []);

  // Detect virtual keyboard
  useEffect(() => {
    if (!isMobile) return;

    // Store initial viewport height
    initialViewportHeight.current = window.innerHeight;

    const detectKeyboard = () => {
      // If viewport height significantly decreases, keyboard is likely open
      // Using a threshold of 15% of the screen height
      const heightThreshold = initialViewportHeight.current * 0.15;
      const heightDifference = initialViewportHeight.current - window.innerHeight;

      setIsKeyboardOpen(heightDifference > heightThreshold);
    };

    window.addEventListener("resize", detectKeyboard);

    return () => {
      window.removeEventListener("resize", detectKeyboard);
    };
  }, [isMobile]);

  useEffect(() => {
    if (!isVisible || !inputRef.current || !dropdownRef.current) return;

    // Position the dropdown intelligently based on available space
    const updatePosition = () => {
      if (!inputRef.current || !dropdownRef.current) return;

      const rect = inputRef.current.getBoundingClientRect();
      const inputHeight = rect.height;

      // Add a small buffer to ensure there's a gap between input and dropdown
      const buffer = 5;

      // Calculate dropdown height - account for header (28px) and items
      const itemHeight = 44; // Each item is approximately 44px tall (including borders)
      const headerHeight = 28; // Header is approximately 28px tall
      const dropdownHeight = Math.min(
        headerHeight + predictions.length * itemHeight,
        headerHeight + 5 * itemHeight // Max 5 items visible before scrolling
      );

      // Calculate available space
      const spaceBelow = window.innerHeight - rect.bottom - buffer;
      const spaceAbove = rect.top - buffer;

      // On mobile devices, ALWAYS position the dropdown above the input
      // This ensures it's never hidden by the virtual keyboard
      // For desktop, use the smart positioning logic
      const newPosition = isMobile
        ? "top"
        : spaceBelow >= dropdownHeight || spaceBelow >= spaceAbove
          ? "bottom"
          : "top";

      setPosition(newPosition);

      // Set position styles
      dropdownRef.current.style.position = "fixed";
      dropdownRef.current.style.width = `${rect.width}px`;
      dropdownRef.current.style.left = `${rect.left}px`;

      if (newPosition === "bottom") {
        // Position below the input with a small gap
        dropdownRef.current.style.top = `${rect.bottom + buffer}px`;
        dropdownRef.current.style.bottom = "auto";
        dropdownRef.current.style.maxHeight = `${window.innerHeight - rect.bottom - buffer * 2}px`;
      } else {
        // Position above the input with a small gap
        // For mobile, use a more direct approach to ensure it's above the input
        if (isMobile) {
          const dropdownHeight = Math.min(predictions.length * 44 + 28, 240);
          dropdownRef.current.style.bottom = `${window.innerHeight - rect.top + buffer}px`;
          dropdownRef.current.style.top = "auto";
          dropdownRef.current.style.maxHeight = `${rect.top - buffer * 2}px`;
        } else {
          // For desktop, use the standard approach
          dropdownRef.current.style.top = `${rect.top - dropdownHeight - buffer}px`;
          dropdownRef.current.style.bottom = "auto";
          dropdownRef.current.style.maxHeight = `${rect.top - buffer * 2}px`;
        }
      }
    };

    // Update position initially and on resize
    updatePosition();
    window.addEventListener("resize", updatePosition);
    window.addEventListener("scroll", updatePosition);

    return () => {
      window.removeEventListener("resize", updatePosition);
      window.removeEventListener("scroll", updatePosition);
    };
  }, [isVisible, inputRef, predictions.length]);

  if (!isVisible) return null;

  // Create portal to render the dropdown at the document body level
  return createPortal(
    <div
      ref={dropdownRef}
      className={`z-[99999] bg-white border rounded-md shadow-xl overflow-y-auto transition-all duration-200 animate-in fade-in-50 ${
        position === "top"
          ? "origin-bottom slide-in-from-bottom-2"
          : "origin-top slide-in-from-top-2"
      } ${isMobile ? "mobile-dropdown" : ""}`}
      style={{
        position: "fixed",
        zIndex: 99999,
      }}
      onMouseDown={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
    >
      {predictions.length > 0 ? (
        <>
          <div className="sticky top-0 bg-gray-50 px-4 py-2 text-xs font-medium text-gray-500 border-b">
            {isMobile ? "Tap to select a location" : "Select a location"}
          </div>
          {predictions.map((place, index) => (
            <div
              key={index}
              className={`px-4 py-3 cursor-pointer transition-colors border-b border-gray-100 last:border-0 flex items-center ${
                isMobile ? "py-4 active:bg-gray-100" : "hover:bg-gray-50"
              }`}
              onMouseDown={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onSelect(place);
                // On mobile, blur the input to hide keyboard after selection
                if (isMobile && inputRef.current) {
                  inputRef.current.blur();
                }
              }}
            >
              <div className="mr-2 text-gray-400">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width={isMobile ? "18" : "16"}
                  height={isMobile ? "18" : "16"}
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                  <circle cx="12" cy="10" r="3"></circle>
                </svg>
              </div>
              <div className={`${isMobile ? "text-base" : "text-sm"}`}>{place.description}</div>
            </div>
          ))}
        </>
      ) : (
        <div className={`px-4 py-3 ${isMobile ? "text-base" : "text-sm"} text-gray-500`}>
          No locations found
        </div>
      )}
    </div>,
    document.body
  );
};

export default LocationDropdown;
