{"name": "amuzn-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0 -p 3000", "build": "set NODE_OPTIONS=--trace-warnings && next build", "build:debug": "set NODE_OPTIONS=--inspect && next build", "start": "next start", "lint": "next lint", "lint:all": "npx eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "npx eslint \"src/**/*.{js,jsx,ts,tsx}\" --fix", "lint:fix:all": "node fix-eslint.js", "codegen": "graphql-codegen"}, "dependencies": {"@apollo/client": "^3.12.5", "@graphql-codegen/cli": "^5.0.3", "@graphql-codegen/fragment-matcher": "^5.0.2", "@graphql-codegen/typescript": "^4.1.1", "@graphql-codegen/typescript-operations": "^4.3.1", "@graphql-codegen/typescript-react-query": "^6.1.0", "@heroui/autocomplete": "^2.3.17", "@heroui/chip": "^2.2.12", "@heroui/drawer": "^2.2.16", "@heroui/progress": "^2.2.12", "@heroui/react": "^2.7.5", "@heroui/system": "^2.4.12", "@heroui/theme": "^2.4.12", "@lens-chain/sdk": "^1.0.3", "@lens-chain/storage-client": "^1.0.4", "@notionhq/client": "^3.1.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@reduxjs/toolkit": "^2.5.0", "@stripe/react-stripe-js": "^3.9.0", "@stripe/stripe-js": "^7.8.0", "@thirdweb-dev/react": "3.16.5", "@thirdweb-dev/sdk": "^3.10.67", "@types/dompurify": "^3.2.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "coinbase-commerce-node": "^1.0.4", "connectkit": "^1.9.0", "date-fns": "^4.1.0", "dompurify": "^3.2.5", "embla-carousel-react": "^8.5.1", "firebase": "^11.0.2", "firebase-admin": "^13.4.0", "framer-motion": "^12.5.0", "graphql": "^16.9.0", "graphql-codegen": "^0.4.0", "ipfs-http-client": "^60.0.1", "lines-and-columns": "^2.0.4", "lucide-react": "^0.460.0", "next": "15.0.3", "next-themes": "^0.4.3", "ngrok": "^5.0.0-beta.2", "omit-deep": "^0.3.0", "photoswipe": "^5.4.4", "pino-pretty": "^13.0.0", "react": "^18.3.1", "react-calendar": "^5.1.0", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-feather": "^2.0.10", "react-photoswipe-gallery": "^3.1.0", "react-query": "^4.0.0", "react-redux": "^9.2.0", "sonner": "^1.7.0", "strip-ansi": "^7.1.0", "stripe": "^18.3.0", "subscriptions-transport-ws": "^0.11.0", "swiper": "^11.2.5", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "thirdweb": "^5.96.4", "uuid": "^11.0.5", "value-or-promise": "^1.0.12", "viem": "^2.29.2", "wagmi": "^2.14.8", "wrap-ansi": "^9.0.0", "yet-another-react-lightbox": "^3.21.8"}, "devDependencies": {"@types/node": "^20", "@types/omit-deep": "^0.3.2", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "babel-eslint": "^9.0.0", "eslint": "^9.27.0", "eslint-config-next": "^15.3.2", "eslint-config-prettier": "^10.1.5", "eslint-config-react-app": "^7.0.1", "eslint-plugin-flowtype": "^2.50.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "postcss": "^8", "prettier": "^3.5.3", "tailwindcss": "^3.4.1", "typescript": "^5"}}